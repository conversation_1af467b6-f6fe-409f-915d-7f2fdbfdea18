plugins {
    alias(libs.plugins.android.library)
}

android {
    namespace 'com.mojang.minecraftpe'
    compileSdk 34
    defaultConfig {
        minSdk 26
        consumerProguardFiles "consumer-rules.pro"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {}